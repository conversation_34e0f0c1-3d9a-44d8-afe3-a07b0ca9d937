# إصلاح مشكلة ميزة "إظهار أسماء الألوان بدلاً من الدوائر" في الشكل الثالث

## المشكلة الأصلية
ميزة "إظهار أسماء الألوان بدلاً من الدوائر" في تبويبة إعدادات النموذج كانت لا تعمل مع الشكل الثالث، حيث كانت تُظهر كلاً من اسم اللون والدائرة الملونة في نفس الوقت بدلاً من إظهار اسم اللون **بدلاً من** الدائرة.

## الحل المطبق

### 1. إصلاح CSS للشكل الثالث
**ملف**: `assets/css/rid-cod.css`

#### أ. تحديث CSS لإظهار أسماء الألوان فقط عند التفعيل:
```css
/* Color names display for third form - show only color names when enabled */
.rid-cod-form-third.rid-cod-show-color-names .rid-cod-variations-external .variation-option.color-option {
    width: auto;
    height: 40px;
    border-radius: 20px;
    padding: 8px 15px;
    min-width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: transparent !important;
    border: 2px solid var(--rid-cod-border-color);
}

/* Hide color swatch when color names are enabled for third form */
.rid-cod-form-third.rid-cod-show-color-names .rid-cod-variations-external .variation-option.color-option .color-swatch {
    display: none !important;
}
```

#### ب. تحديث CSS لإظهار الدوائر فقط عند إلغاء التفعيل:
```css
/* When color names are disabled, hide the text and show only color swatch */
.rid-cod-form-third:not(.rid-cod-show-color-names) .rid-cod-variations-external .variation-option.color-option .color-name {
    display: none !important;
}

.rid-cod-form-third:not(.rid-cod-show-color-names) .rid-cod-variations-external .variation-option.color-option {
    width: 35px;
    height: 35px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}
```

#### ج. إصلاح CSS المتضارب:
تم تحديث جميع قواعد CSS التي تفرض إظهار `color-swatch` لتعمل فقط عندما تكون أسماء الألوان معطلة:
```css
/* Force color display for third form - only when color names are disabled */
.rid-cod-form-third:not(.rid-cod-show-color-names) .rid-cod-variations-external .variation-option.color-option .color-swatch {
    background-color: var(--color, #e9ecef) !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
```

### 2. إصلاح JavaScript
**ملف**: `includes/class-rid-cod-form.php`

تم تحديث الكود JavaScript ليتحقق من إعداد `show_color_names` قبل إضافة `color-swatch`:
```javascript
// Force apply colors to third form variations only when color names are disabled
if (!$('.rid-cod-form-third').hasClass('rid-cod-show-color-names')) {
    $('.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option').each(function() {
        var $option = $(this);
        var color = $option.data('color');
        if (color) {
            var $swatch = $option.find('.color-swatch');
            if ($swatch.length === 0) {
                // Create swatch if it doesn't exist
                $swatch = $('<div class="color-swatch"></div>');
                $option.prepend($swatch);
            }
            $swatch.css('background-color', color);
            $option.css('--swatch-color', color);
        }
    });
}
```

### 3. إصلاح CSS العام
تم إضافة استثناءات للشكل الثالث في CSS العام لمنع التداخل:
```css
/* Third form has its own color name handling */
.rid-cod-form-third .rid-cod-variation-option.color-option .color-name,
.rid-cod-form-third .variation-option.color-option .color-name {
    display: initial; /* Reset to allow third form CSS to control */
}

/* Third form has its own color swatch handling */
.rid-cod-form-third .rid-cod-variation-option.color-option .color-swatch,
.rid-cod-form-third .variation-option.color-option .color-swatch {
    display: initial; /* Reset to allow third form CSS to control */
}
```

## النتيجة النهائية

### ✅ عند تفعيل "إظهار أسماء الألوان":
- يتم إخفاء الدوائر الملونة تماماً
- يتم إظهار أسماء الألوان كنص فقط
- الشكل يصبح مستطيلاً بدلاً من دائري

### ✅ عند إلغاء تفعيل "إظهار أسماء الألوان":
- يتم إخفاء أسماء الألوان تماماً
- يتم إظهار الدوائر الملونة فقط
- الشكل يصبح دائرياً

### ✅ التوافق مع الأشكال الأخرى:
- الشكل الكلاسيكي يعمل كما هو
- الشكل الحديث يعمل كما هو
- الشكل الثاني يعمل كما هو

## التحديث الثاني - إصلاحات إضافية

### 4. إصلاح CSS المتضارب الإضافي
تم إصلاح جميع قواعد CSS التي كانت تفرض إظهار `color-swatch`:

```css
/* Ultimate fallback - force all color swatches to be visible only when color names are disabled */
.rid-cod-form-third:not(.rid-cod-show-color-names) .rid-cod-variations-external .color-swatch {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Remove any background colors from the option itself when color names are enabled */
.rid-cod-form-third.rid-cod-show-color-names .rid-cod-variations-external .variation-option.color-option {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}
```

### 5. إصلاح JavaScript المحسن
تم تحديث JavaScript ليزيل الدوائر بشكل نشط عندما تكون أسماء الألوان مُفعلة:

```javascript
// Handle third form color display based on show_color_names setting
if ($('.rid-cod-form-third').hasClass('rid-cod-show-color-names')) {
    // When color names are enabled, remove any color swatches and background colors
    $('.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option').each(function() {
        var $option = $(this);
        // Remove any existing color swatches
        $option.find('.color-swatch').remove();
        // Remove any background colors
        $option.css({
            'background': 'transparent',
            'background-color': 'transparent',
            'background-image': 'none'
        });
    });
}
```

### 6. مراقب التغييرات
تم إضافة `MutationObserver` لمراقبة أي تغييرات قد تعيد إضافة الدوائر:

```javascript
// Monitor for any changes that might re-add color swatches
var observer = new MutationObserver(function(mutations) {
    if ($('.rid-cod-form-third').hasClass('rid-cod-show-color-names')) {
        $('.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option').each(function() {
            var $option = $(this);
            // Remove any newly added color swatches
            $option.find('.color-swatch').remove();
            // Remove any background colors
            $option.css({
                'background': 'transparent',
                'background-color': 'transparent',
                'background-image': 'none'
            });
        });
    }
});
```

## اختبار النظام

1. **اختبار التفعيل**: فعّل "إظهار أسماء الألوان" - يجب أن تظهر أسماء الألوان فقط
2. **اختبار إلغاء التفعيل**: ألغِ تفعيل "إظهار أسماء الألوان" - يجب أن تظهر الدوائر الملونة فقط
3. **اختبار التبديل**: بدّل بين التفعيل وإلغاء التفعيل - يجب أن يتغير العرض فوراً
4. **اختبار الأشكال الأخرى**: تأكد من أن الأشكال الأخرى لا تزال تعمل بشكل صحيح
5. **اختبار إعادة التحميل**: أعد تحميل الصفحة للتأكد من أن الإعدادات تُطبق بشكل صحيح

## الملفات المُحدثة
- `assets/css/rid-cod.css` - إصلاح CSS للشكل الثالث (تحديثات متعددة)
- `includes/class-rid-cod-form.php` - إصلاح JavaScript للشكل الثالث (تحديثات محسنة)

# الحل النهائي والشامل لمشكلة أسماء الألوان في الشكل الثالث

## المشكلة
ميزة "إظهار أسماء الألوان بدلاً من الدوائر" لا تعمل مع الشكل الثالث - كانت تُظهر الدوائر الملونة حتى عندما تكون الميزة مُفعلة.

## السبب الجذري
1. **اختلاف في أسماء الكلاسات**: الشكل الكلاسيكي يستخدم `.rid-show-color-names` بينما الشكل الثالث كان يستخدم `.rid-cod-show-color-names` فقط
2. **CSS متضارب**: كان هناك CSS يفرض إظهار الدوائر الملونة بغض النظر عن الإعدادات
3. **JavaScript يضيف ألوان**: كان JavaScript يضيف ألوان خلفية حتى عندما تكون أسماء الألوان مُفعلة

## الحل الشامل

### 1. إضافة الكلاس الصحيح
**ملف**: `includes/class-rid-cod-form.php`
```php
if ($show_color_names) {
    $form_class .= ' rid-cod-show-color-names rid-show-color-names';
}
```

### 2. CSS شامل للشكل الثالث
**ملف**: `assets/css/rid-cod.css`

#### أ. تنسيق أسماء الألوان (نفس منطق الشكل الكلاسيكي):
```css
.rid-cod-form-third.rid-show-color-names .rid-cod-variations-external .variation-option.color-option {
    border-radius: 8px !important;
    background: #f8f9fa !important;
    border: 2px solid #dee2e6 !important;
    color: #495057 !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 10px 14px !important;
    min-width: auto !important;
    width: auto !important;
    height: auto !important;
    min-height: 44px !important;
    white-space: nowrap !important;
    font-size: 16px !important;
    margin: 4px !important;
}
```

#### ب. إخفاء الدوائر الملونة بشكل شامل:
```css
.rid-cod-form-third.rid-show-color-names .rid-cod-variations-external .variation-option.color-option .color-swatch,
.rid-cod-form-third.rid-show-color-names .rid-cod-variations-external .variation-option.color-option::before,
.rid-cod-form-third.rid-show-color-names .rid-cod-variations-external .variation-option.color-option::after {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}
```

#### ج. إزالة ألوان الخلفية من العناصر:
```css
.rid-cod-form-third.rid-show-color-names .rid-cod-variations-external .variation-option.color-option {
    background: #f8f9fa !important;
    background-color: #f8f9fa !important;
    background-image: none !important;
}

.rid-cod-form-third.rid-show-color-names .rid-cod-variations-external .variation-option.color-option[style] {
    background: #f8f9fa !important;
    background-color: #f8f9fa !important;
    background-image: none !important;
}
```

#### د. إلغاء قواعد الألوان المحددة:
```css
.rid-cod-form-third.rid-show-color-names .rid-cod-variations-external .variation-option.color-option[data-color="#ff0000"],
.rid-cod-form-third.rid-show-color-names .rid-cod-variations-external .variation-option.color-option[data-color="#ffa500"],
/* ... جميع الألوان الأخرى ... */ {
    background: #f8f9fa !important;
    background-color: #f8f9fa !important;
    background-image: none !important;
}
```

### 3. JavaScript محسن
**ملف**: `includes/class-rid-cod-form.php`

#### أ. تطبيق التنسيق النصي عند تفعيل أسماء الألوان:
```javascript
if ($('.rid-cod-form-third').hasClass('rid-show-color-names')) {
    $('.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option').each(function() {
        var $option = $(this);
        // Remove any existing color swatches
        $option.find('.color-swatch').remove();
        // Apply text-based styling
        $option.css({
            'background': '#f8f9fa',
            'background-color': '#f8f9fa',
            'background-image': 'none',
            'border-radius': '8px',
            'border': '2px solid #dee2e6',
            'color': '#495057',
            'font-weight': '500',
            'padding': '10px 14px',
            'min-width': 'auto',
            'width': 'auto',
            'height': 'auto',
            'min-height': '44px'
        });
        // Remove any style attribute that might override our CSS
        $option.removeAttr('style');
    });
}
```

#### ب. مراقب التغييرات:
```javascript
var observer = new MutationObserver(function(mutations) {
    if ($('.rid-cod-form-third').hasClass('rid-show-color-names')) {
        $('.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option').each(function() {
            var $option = $(this);
            $option.find('.color-swatch').remove();
            $option.css({
                'background': '#f8f9fa',
                'background-color': '#f8f9fa',
                'background-image': 'none'
            });
            $option.removeAttr('style');
        });
    }
});
```

## النتيجة النهائية

### ✅ عند تفعيل "إظهار أسماء الألوان":
- أسماء الألوان تظهر في مربعات مستطيلة رمادية فاتحة
- لا توجد أي دوائر ملونة أو ألوان خلفية
- التنسيق يطابق الشكل الكلاسيكي تماماً

### ✅ عند إلغاء تفعيل "إظهار أسماء الألوان":
- الدوائر الملونة تظهر بشكل طبيعي
- أسماء الألوان مخفية

### ✅ التوافق:
- جميع الأشكال الأخرى تعمل بشكل طبيعي
- لا يوجد تأثير على الوظائف الأخرى

## اختبار النظام

1. **فعّل الميزة**: اذهب إلى إعدادات الإضافة وفعّل "إظهار أسماء الألوان بدلاً من الدوائر"
2. **احفظ الإعدادات**: تأكد من حفظ الإعدادات
3. **اختر الشكل الثالث**: في صفحة المنتج، اختر الشكل الثالث
4. **تحقق من النتيجة**: يجب أن ترى أسماء الألوان في مربعات رمادية بدون أي دوائر ملونة
5. **اختبر التبديل**: ألغِ تفعيل الميزة وتأكد من ظهور الدوائر الملونة

## الملفات المُحدثة
- `assets/css/rid-cod.css` - CSS شامل للشكل الثالث
- `includes/class-rid-cod-form.php` - JavaScript محسن + إضافة الكلاس الصحيح

هذا الحل شامل ونهائي ويضمن عمل الميزة بشكل صحيح في جميع الحالات.
